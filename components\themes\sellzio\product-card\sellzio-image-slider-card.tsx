"use client"

import React, { useState } from "react"
import Image from "next/image"
import {
  SellzioMallBadge,
  SellzioStarBadge,
  SellzioStarLiteBadge,
  SellzioCodBadge,
  SellzioDiscountBadge,
  SellzioRatingStars,
  SellzioShippingBadge,
  SellzioTerlarisBadge,
  SellzioLiveBadge,
  SellzioTermurahDiTokoBadge,
  SellzioKomisiXtraBadge,
} from "./sellzio-badges"

export interface SellzioImageSliderCardProps {
  id: number
  name: string
  price: string
  originalPrice?: string
  discount?: string
  images: string[] // Array of image URLs
  rating?: number
  sold?: number
  hasCod?: boolean
  isMall?: boolean
  badgeType?: "mall" | "star" | "star-lite" | "termurah" | "terlaris" | "komisi-xtra" | "none"
  shipping?: string
  onClick?: () => void
}

export const SellzioImageSliderCard = ({
  id,
  name,
  price,
  originalPrice,
  discount,
  images = [],
  rating = 0,
  sold = 0,
  hasCod = false,
  isMall = false,
  badgeType = "none",
  shipping = "Pengiriman Instan",
  onClick
}: SellzioImageSliderCardProps) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [imageLoaded, setImageLoaded] = useState(false)

  const getShippingType = (): "instan" | "gratis" | "reguler" => {
    if (shipping?.toLowerCase().includes("instan")) return "instan"
    if (shipping?.toLowerCase().includes("gratis")) return "gratis"
    return "reguler"
  }

  const nextImage = (e: React.MouseEvent) => {
    e.stopPropagation()
    setCurrentImageIndex((prev) => (prev + 1) % images.length)
    setImageLoaded(false)
  }

  const prevImage = (e: React.MouseEvent) => {
    e.stopPropagation()
    setCurrentImageIndex((prev) => (prev - 1 + images.length) % images.length)
    setImageLoaded(false)
  }

  const goToImage = (index: number, e: React.MouseEvent) => {
    e.stopPropagation()
    setCurrentImageIndex(index)
    setImageLoaded(false)
  }

  return (
    <div className="sellzio-image-slider-card" onClick={onClick}>
      {/* Image Container with Slider */}
      <div className="sellzio-image-container">
        {/* Corner badges */}
        {hasCod && <SellzioCodBadge />}
        {discount && <SellzioDiscountBadge discount={discount} />}

        {/* Main Image */}
        <div className="sellzio-main-image">
          {!imageLoaded && (
            <div className="sellzio-image-placeholder">
              <div className="sellzio-loading-shimmer"></div>
            </div>
          )}
          <Image
            src={images[currentImageIndex] || "/api/placeholder/200/200"}
            alt={name}
            fill
            className={`sellzio-product-image ${imageLoaded ? "loaded" : ""}`}
            onLoad={() => setImageLoaded(true)}
            sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
          />
        </div>

        {/* Navigation Arrows */}
        {images.length > 1 && (
          <>
            <button
              className="sellzio-nav-arrow sellzio-nav-prev"
              onClick={prevImage}
              aria-label="Previous image"
            >
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-4 h-4">
                <path fillRule="evenodd" d="M7.72 12.53a.75.75 0 010-1.06l7.5-7.5a.75.75 0 111.06 1.06L9.31 12l6.97 6.97a.75.75 0 11-1.06 1.06l-7.5-7.5z" clipRule="evenodd" />
              </svg>
            </button>
            <button
              className="sellzio-nav-arrow sellzio-nav-next"
              onClick={nextImage}
              aria-label="Next image"
            >
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-4 h-4">
                <path fillRule="evenodd" d="M16.28 11.47a.75.75 0 010 1.06l-7.5 7.5a.75.75 0 01-1.06-1.06L14.69 12 7.72 5.03a.75.75 0 011.06-1.06l7.5 7.5z" clipRule="evenodd" />
              </svg>
            </button>
          </>
        )}

        {/* Image Dots Indicator */}
        {images.length > 1 && (
          <div className="sellzio-image-dots">
            {images.map((_, index) => (
              <button
                key={index}
                className={`sellzio-dot ${index === currentImageIndex ? "active" : ""}`}
                onClick={(e) => goToImage(index, e)}
                aria-label={`Go to image ${index + 1}`}
              />
            ))}
          </div>
        )}
      </div>

      {/* Product Info */}
      <div className="sellzio-product-info">
        {/* Product Name with badges */}
        <h3 className="sellzio-product-name">
          {/* Render badges based on priority */}
          {badgeType === "mall" || isMall ? (
            <SellzioMallBadge />
          ) : badgeType === "star" ? (
            <SellzioStarBadge />
          ) : badgeType === "star-lite" ? (
            <SellzioStarLiteBadge />
          ) : null}
          {name}
        </h3>

        {/* Special badges below name */}
        {badgeType === "termurah" && <SellzioTermurahDiTokoBadge />}
        {badgeType === "terlaris" && <SellzioTerlarisBadge />}
        {badgeType === "komisi-xtra" && <SellzioKomisiXtraBadge />}

        {/* Rating and Sold */}
        <div className="sellzio-product-meta">
          <SellzioRatingStars rating={rating} />
          <div className="sellzio-meta-divider"></div>
          <span className="sellzio-sold-count">Terjual {sold}</span>
        </div>

        {/* Shipping */}
        <SellzioShippingBadge type={getShippingType()} />

        {/* Price */}
        <div className="sellzio-price-container">
          <div className="sellzio-current-price">{price}</div>
          {originalPrice && (
            <div className="sellzio-original-price">{originalPrice}</div>
          )}
        </div>
      </div>

      <style jsx>{`
        .sellzio-image-slider-card {
          background-color: white;
          border-radius: 8px;
          overflow: hidden;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          cursor: pointer;
          transition: all 0.2s ease;
          width: 100%;
          border: 1px solid #f3f4f6;
        }

        .sellzio-image-slider-card:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }

        .sellzio-image-container {
          position: relative;
          width: 100%;
          padding-top: 100%; /* 1:1 Aspect Ratio */
          overflow: hidden;
          background-color: #f9fafb;
        }

        .sellzio-main-image {
          position: absolute;
          inset: 0;
        }

        .sellzio-product-image {
          object-fit: contain;
          transition: opacity 0.3s ease;
          opacity: 0;
          padding: 8px;
        }

        .sellzio-product-image.loaded {
          opacity: 1;
        }

        .sellzio-image-placeholder {
          position: absolute;
          inset: 0;
          background-color: #f3f4f6;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .sellzio-loading-shimmer {
          width: 60%;
          height: 60%;
          background: linear-gradient(
            90deg,
            #f3f4f6 25%,
            #e5e7eb 50%,
            #f3f4f6 75%
          );
          background-size: 200% 100%;
          animation: shimmer 1.5s infinite;
          border-radius: 4px;
        }

        @keyframes shimmer {
          0% {
            background-position: -200% 0;
          }
          100% {
            background-position: 200% 0;
          }
        }

        .sellzio-nav-arrow {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          background-color: rgba(0, 0, 0, 0.5);
          color: white;
          border: none;
          border-radius: 50%;
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.2s ease;
          z-index: 10;
          opacity: 0;
        }

        .sellzio-image-container:hover .sellzio-nav-arrow {
          opacity: 1;
        }

        .sellzio-nav-arrow:hover {
          background-color: rgba(0, 0, 0, 0.7);
          transform: translateY(-50%) scale(1.1);
        }

        .sellzio-nav-prev {
          left: 8px;
        }

        .sellzio-nav-next {
          right: 8px;
        }

        .sellzio-image-dots {
          position: absolute;
          bottom: 8px;
          left: 50%;
          transform: translateX(-50%);
          display: flex;
          gap: 4px;
          z-index: 10;
        }

        .sellzio-dot {
          width: 6px;
          height: 6px;
          border-radius: 50%;
          border: none;
          background-color: rgba(255, 255, 255, 0.5);
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .sellzio-dot.active {
          background-color: #ee4d2d;
          transform: scale(1.2);
        }

        .sellzio-dot:hover {
          background-color: rgba(255, 255, 255, 0.8);
        }

        .sellzio-product-info {
          padding: 12px;
        }

        .sellzio-product-name {
          font-size: 13px;
          font-weight: 500;
          color: #374151;
          line-height: 1.4;
          margin-bottom: 8px;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          min-height: 36px;
        }

        .sellzio-product-meta {
          display: flex;
          align-items: center;
          margin-bottom: 8px;
          font-size: 11px;
          color: #6b7280;
        }

        .sellzio-meta-divider {
          width: 1px;
          height: 12px;
          background-color: #d1d5db;
          margin: 0 8px;
        }

        .sellzio-sold-count {
          font-size: 11px;
          color: #6b7280;
        }

        .sellzio-price-container {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-top: 8px;
        }

        .sellzio-current-price {
          font-size: 14px;
          font-weight: 700;
          color: #f97316;
        }

        .sellzio-original-price {
          font-size: 12px;
          color: #9ca3af;
          text-decoration: line-through;
        }

        @media (max-width: 768px) {
          .sellzio-image-slider-card {
            border-radius: 6px;
          }

          .sellzio-product-info {
            padding: 10px;
          }

          .sellzio-product-name {
            font-size: 12px;
            min-height: 32px;
          }

          .sellzio-current-price {
            font-size: 13px;
          }

          .sellzio-nav-arrow {
            width: 28px;
            height: 28px;
          }
        }
      `}</style>
    </div>
  )
}
