"use client"

import React from "react"

// Mall Badge
export const SellzioMallBadge = () => (
  <span className="sellzio-mall-badge">
    Mall
  </span>
)

// COD Badge
export const SellzioCodBadge = () => (
  <div className="sellzio-cod-badge">
    <i className="fa fa-money-bill"></i>
    COD
  </div>
)

// Discount Badge
export const SellzioDiscountBadge = ({ discount }: { discount: string }) => (
  <div className="sellzio-discount-badge">
    -{discount}
  </div>
)

// Rating Stars
export const SellzioRatingStars = ({ rating }: { rating: number }) => {
  const fullStars = Math.floor(rating)
  const hasHalfStar = rating % 1 !== 0

  return (
    <div className="sellzio-rating-stars">
      {[...Array(5)].map((_, index) => {
        if (index < fullStars) {
          return <i key={index} className="fa fa-star sellzio-star-filled"></i>
        } else if (index === fullStars && hasHalfStar) {
          return <i key={index} className="fa fa-star-half-alt sellzio-star-half"></i>
        } else {
          return <i key={index} className="fa fa-star sellzio-star-empty"></i>
        }
      })}
      <span className="sellzio-rating-text">{rating.toFixed(1)}</span>
    </div>
  )
}

// Shipping Badge
export const SellzioShippingBadge = ({ type }: { type: "instan" | "gratis" | "reguler" }) => {
  const getShippingText = () => {
    switch (type) {
      case "instan":
        return "Pengiriman Instan"
      case "gratis":
        return "Gratis Ongkir"
      case "reguler":
        return "Pengiriman Reguler"
      default:
        return "Pengiriman"
    }
  }

  return (
    <div className="sellzio-shipping-badge">
      <i className="fa fa-truck"></i>
      {getShippingText()}
    </div>
  )
}

// Terlaris Badge
export const SellzioTerlarisBadge = () => (
  <div className="sellzio-terlaris-badge">
    <i className="fa fa-fire"></i>
    Terlaris
  </div>
)

// Live Badge
export const SellzioLiveBadge = () => (
  <div className="sellzio-live-badge">
    <i className="fa fa-circle sellzio-live-dot"></i>
    LIVE
  </div>
)

// CSS Styles
export const SellzioBadgeStyles = () => (
  <style jsx global>{`
    .sellzio-mall-badge {
      display: inline-block;
      background-color: #f97316;
      color: white;
      font-size: 10px;
      font-weight: 600;
      padding: 2px 6px;
      border-radius: 3px;
      margin-right: 4px;
      text-transform: uppercase;
    }

    .sellzio-cod-badge {
      position: absolute;
      bottom: 8px;
      left: 8px;
      background-color: rgba(0, 0, 0, 0.7);
      color: white;
      font-size: 10px;
      font-weight: 500;
      padding: 4px 6px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      gap: 4px;
      z-index: 2;
    }

    .sellzio-discount-badge {
      position: absolute;
      top: 8px;
      left: 8px;
      background-color: #ef4444;
      color: white;
      font-size: 11px;
      font-weight: 600;
      padding: 4px 6px;
      border-radius: 4px;
      z-index: 2;
    }

    .sellzio-rating-stars {
      display: flex;
      align-items: center;
      gap: 2px;
      font-size: 11px;
    }

    .sellzio-star-filled {
      color: #fbbf24;
    }

    .sellzio-star-half {
      color: #fbbf24;
    }

    .sellzio-star-empty {
      color: #d1d5db;
    }

    .sellzio-rating-text {
      margin-left: 4px;
      font-size: 11px;
      color: #6b7280;
    }

    .sellzio-shipping-badge {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 10px;
      color: #059669;
      margin-bottom: 8px;
    }

    .sellzio-terlaris-badge {
      display: inline-flex;
      align-items: center;
      gap: 4px;
      background-color: #dc2626;
      color: white;
      font-size: 10px;
      font-weight: 600;
      padding: 2px 6px;
      border-radius: 3px;
      margin-right: 4px;
    }

    .sellzio-live-badge {
      position: absolute;
      top: 8px;
      right: 8px;
      background-color: #ef4444;
      color: white;
      font-size: 10px;
      font-weight: 600;
      padding: 4px 8px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      gap: 4px;
      z-index: 2;
    }

    .sellzio-live-dot {
      animation: sellzio-pulse 2s infinite;
    }

    @keyframes sellzio-pulse {
      0%, 100% {
        opacity: 1;
      }
      50% {
        opacity: 0.5;
      }
    }
  `}</style>
)
