"use client"

import React from "react"

// Mall Badge - sesuai dengan Velozio
export const SellzioMallBadge = () => (
  <div className="sellzio-mall-badge">
    <div className="sellzio-mall-text">Mall</div>
    <div className="sellzio-mall-divider"></div>
    <div className="sellzio-mall-ori">ORI</div>
  </div>
)

// Star Badge - sesuai dengan Velozio
export const SellzioStarBadge = ({ hasPlus = true }: { hasPlus?: boolean }) => (
  <div className="sellzio-star-badge">
    <div className="sellzio-star-text">Star</div>
    {hasPlus && <div className="sellzio-star-plus">Plus</div>}
  </div>
)

// Star Lite Badge - sesuai dengan Velozio
export const SellzioStarLiteBadge = () => (
  <div className="sellzio-star-badge">
    <div className="sellzio-star-text">Star</div>
    <div className="sellzio-star-lite">Lite</div>
  </div>
)

// COD Badge - sesuai dengan Velozio (di pojok kanan atas)
export const SellzioCodBadge = () => (
  <div className="sellzio-cod-badge">
    COD
  </div>
)

// Discount Badge - sesuai dengan Velozio (di pojok kanan atas)
export const SellzioDiscountBadge = ({ discount }: { discount: string }) => (
  <div className="sellzio-discount-badge">
    -{discount}
  </div>
)

// Rating Stars - sesuai dengan Velozio
export const SellzioRatingStars = ({ rating = 0 }: { rating?: number }) => {
  const safeRating = typeof rating === "number" && !isNaN(rating) ? rating : 0
  const fullStars = Math.floor(safeRating)
  const hasHalfStar = rating % 1 >= 0.5

  return (
    <div className="sellzio-rating-stars">
      {Array.from({ length: 5 }).map((_, index) => {
        if (index < fullStars) {
          return (
            <svg
              key={index}
              xmlns="http://www.w3.org/2000/svg"
              width="10"
              height="10"
              viewBox="0 0 24 24"
              fill="currentColor"
              className="sellzio-star-filled"
            >
              <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2" />
            </svg>
          )
        } else if (index === fullStars && hasHalfStar) {
          return (
            <svg
              key={index}
              xmlns="http://www.w3.org/2000/svg"
              width="10"
              height="10"
              viewBox="0 0 24 24"
              className="sellzio-star-half"
            >
              <defs>
                <linearGradient id="half-star" x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="50%" stopColor="#ee4d2d" />
                  <stop offset="50%" stopColor="none" stopOpacity="0" />
                </linearGradient>
              </defs>
              <polygon
                points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"
                fill="url(#half-star)"
                stroke="#ee4d2d"
                strokeWidth="1"
              />
            </svg>
          )
        } else {
          return (
            <svg
              key={index}
              xmlns="http://www.w3.org/2000/svg"
              width="10"
              height="10"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="1"
              className="sellzio-star-empty"
            >
              <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2" />
            </svg>
          )
        }
      })}
      <span className="sellzio-rating-text">{safeRating.toFixed(1)}</span>
    </div>
  )
}

// Shipping Badge - sesuai dengan Velozio
export const SellzioShippingBadge = ({ type = "instan" }: { type?: string }) => {
  const getShippingDetails = () => {
    switch (type.toLowerCase()) {
      case "instan":
        return { color: "#00bfa5", text: "Pengiriman Instan" }
      case "gratis":
        return { color: "#ee4d2d", text: "Gratis Ongkir" }
      case "reguler":
        return { color: "#757575", text: "Pengiriman Reguler" }
      default:
        return { color: "#00bfa5", text: "Pengiriman Instan" }
    }
  }

  const { color, text } = getShippingDetails()

  return (
    <div className="sellzio-shipping-badge">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 640 512"
        className="sellzio-truck-icon"
        fill="currentColor"
        style={{ color }}
      >
        <path d="M48 0C21.5 0 0 21.5 0 48V368c0 26.5 21.5 48 48 48H64c0 53 43 96 96 96s96-43 96-96H384c0 53 43 96 96 96s96-43 96-96h32c17.7 0 32-14.3 32-32s-14.3-32-32-32V288 256 237.3c0-17-6.7-33.3-18.7-45.3L512 114.7c-12-12-28.3-18.7-45.3-18.7H416V48c0-26.5-21.5-48-48-48H48zM416 160h50.7L544 237.3V256H416V160zM112 416a48 48 0 1 1 96 0 48 48 0 1 1 -96 0zm368-48a48 48 0 1 1 0 96 48 48 0 1 1 0-96z" />
      </svg>
      <span>{text}</span>
    </div>
  )
}

// Terlaris Badge - sesuai dengan Velozio
export const SellzioTerlarisBadge = () => (
  <div className="sellzio-terlaris-badge">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="sellzio-trophy-icon">
      <path
        fillRule="evenodd"
        d="M5.166 2.621v.858c-1.035.148-2.059.33-3.071.543a.75.75 0 00-.584.859 6.753 6.753 0 006.138 5.6 6.73 6.73 0 002.743 1.346A6.707 6.707 0 019.279 15H8.54c-1.036 0-1.875.84-1.875 1.875V19.5h-.75a2.25 2.25 0 00-2.25 2.25c0 .414.336.75.75.75h15a.75.75 0 00.75-.75 2.25 2.25 0 00-2.25-2.25h-.75v-2.625c0-1.036-.84-1.875-1.875-1.875h-.739a6.706 6.706 0 01-1.112-3.173 6.73 6.73 0 002.743-1.347 6.753 6.753 0 006.139-********* 0 00-.585-.858 47.077 47.077 0 00-3.07-.543V2.62a.75.75 0 00-.658-.744 49.22 49.22 0 00-6.093-.377c-2.063 0-4.096.128-6.093.377a.75.75 0 00-.657.744zm0 2.629c0 1.196.312 2.32.857 3.294A5.266 5.266 0 013.16 5.337a45.6 45.6 0 012.006-.343v.256zm13.5 0v-.256c.674.1 1.343.214 2.006.343a5.265 5.265 0 01-2.863 3.207 6.72 6.72 0 00.857-3.294z"
        clipRule="evenodd"
      />
    </svg>
    Terlaris
  </div>
)

// Live Badge - sesuai dengan Velozio (inline badge)
export const SellzioLiveBadge = () => (
  <div className="sellzio-live-badge">
    <span className="sellzio-equalizer">
      <span className="sellzio-bar sellzio-bar-1"></span>
      <span className="sellzio-bar sellzio-bar-2"></span>
      <span className="sellzio-bar sellzio-bar-3"></span>
      <span className="sellzio-bar sellzio-bar-4"></span>
    </span>
    LIVE
  </div>
)

// Live Corner Badge - sesuai dengan Velozio (di pojok kiri atas)
export const SellzioLiveCornerBadge = () => (
  <div className="sellzio-live-corner-badge">
    <span className="sellzio-live-dot"></span>
    LIVE
  </div>
)

// Termurah di Toko Badge - sesuai dengan Velozio
export const SellzioTermurahDiTokoBadge = () => (
  <div className="sellzio-termurah-badge">
    <span className="sellzio-termurah-icon">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="sellzio-thumbs-up-icon">
        <path d="M7.493 18.75c-.425 0-.82-.236-.975-.632A7.48 7.48 0 016 15.375c0-1.75.599-3.358 1.602-4.634.151-.192.373-.309.6-.397.473-.183.89-.514 1.212-.924a9.042 9.042 0 012.861-2.4c.723-.384 1.35-.956 1.653-1.715a4.498 4.498 0 00.322-1.672V3a.75.75 0 01.75-.75 2.25 2.25 0 012.25 2.25c0 1.152-.26 2.243-.723 3.218-.266.558.107 1.282.725 1.282h3.126c1.026 0 1.945.694 2.054 1.715.045.422.068.85.068 1.285a11.95 11.95 0 01-2.649 7.521c-.388.482-.987.729-1.605.729H14.23c-.483 0-.964-.078-1.423-.23l-3.114-1.04a4.501 4.501 0 00-1.423-.23h-.777zM2.331 10.977a11.969 11.969 0 00-.831 4.398 12 12 0 00.52 3.507c.26.85 1.084 1.368 1.973 1.368H4.9c.445 0 .72-.498.523-.898a8.963 8.963 0 01-.924-3.977c0-1.708.476-3.305 1.302-4.666.245-.403-.028-.959-.5-.959H4.25c-.832 0-1.612.453-1.918 1.227z" />
      </svg>
    </span>
    Termurah di Toko
  </div>
)

// Komisi Xtra Badge - sesuai dengan Velozio
export const SellzioKomisiXtraBadge = () => (
  <div className="sellzio-komisi-badge">
    <span className="sellzio-komisi-text">KOMISI</span>
    <span className="sellzio-xtra-text">XTRA</span>
  </div>
)

// CSS Styles - sesuai dengan Velozio
export const SellzioBadgeStyles = () => (
  <style jsx global>{`
    /* Mall Badge - sesuai Velozio */
    .sellzio-mall-badge {
      display: inline-flex;
      align-items: center;
      border-radius: 3px;
      overflow: hidden;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      font-size: 10px;
      font-weight: bold;
      height: 14px;
      margin-right: 4px;
      margin-bottom: 4px;
      vertical-align: middle;
    }

    .sellzio-mall-text {
      background-color: #cc0000;
      color: white;
      padding: 0 4px;
      height: 100%;
      display: flex;
      align-items: center;
    }

    .sellzio-mall-divider {
      height: 100%;
      width: 1px;
      background-color: rgba(255, 255, 255, 0.5);
    }

    .sellzio-mall-ori {
      background-color: #cc0000;
      color: white;
      padding: 0 4px;
      height: 100%;
      display: flex;
      align-items: center;
    }

    /* Star Badge - sesuai Velozio */
    .sellzio-star-badge {
      display: inline-flex;
      align-items: center;
      border-radius: 3px;
      overflow: hidden;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      font-size: 10px;
      font-weight: bold;
      height: 14px;
      margin-right: 4px;
      margin-bottom: 4px;
      vertical-align: middle;
    }

    .sellzio-star-text {
      background-color: #ee4d2d;
      color: white;
      padding: 0 4px;
      height: 100%;
      display: flex;
      align-items: center;
    }

    .sellzio-star-plus {
      background-color: #fff0e5;
      color: #ee4d2d;
      padding: 0 4px;
      height: 100%;
      display: flex;
      align-items: center;
    }

    .sellzio-star-lite {
      background-color: #fff0e5;
      color: #ee4d2d;
      padding: 0 4px;
      height: 100%;
      display: flex;
      align-items: center;
    }

    /* COD Badge - di pojok kanan atas */
    .sellzio-cod-badge {
      position: absolute;
      top: 0;
      right: 0;
      background-color: #ee4d2d;
      color: white;
      font-size: 10px;
      font-weight: bold;
      padding: 2px 4px;
      border-bottom-left-radius: 3px;
      z-index: 10;
    }

    /* Discount Badge - di pojok kanan atas */
    .sellzio-discount-badge {
      position: absolute;
      top: 0;
      right: 0;
      background-color: #ee4d2d;
      color: white;
      font-size: 10px;
      font-weight: bold;
      padding: 2px 4px;
      border-bottom-left-radius: 3px;
      z-index: 10;
    }

    /* Rating Stars - sesuai Velozio */
    .sellzio-rating-stars {
      display: flex;
      align-items: center;
      color: #ee4d2d;
    }

    .sellzio-star-filled {
      margin-right: 2px;
      color: #ee4d2d;
    }

    .sellzio-star-half {
      margin-right: 2px;
      color: #ee4d2d;
    }

    .sellzio-star-empty {
      margin-right: 2px;
      color: #ee4d2d;
    }

    .sellzio-rating-text {
      margin-left: 2px;
      font-size: 10px;
      color: #ee4d2d;
    }

    /* Shipping Badge - sesuai Velozio */
    .sellzio-shipping-badge {
      display: flex;
      align-items: center;
      font-size: 10px;
      color: #666;
      margin-bottom: 4px;
    }

    .sellzio-truck-icon {
      width: 16px;
      height: 16px;
      margin-right: 4px;
    }

    /* Terlaris Badge - sesuai Velozio */
    .sellzio-terlaris-badge {
      display: inline-flex;
      align-items: center;
      background-color: white;
      color: #ee4d2d;
      font-size: 10px;
      font-weight: 600;
      padding: 2px 6px;
      border-radius: 2px;
      border: 1px solid #ee4d2d;
      border-left: 3px solid #ee4d2d;
      margin-bottom: 6px;
    }

    .sellzio-trophy-icon {
      width: 12px;
      height: 12px;
      margin-right: 2px;
    }

    /* Live Badge - inline badge sesuai Velozio */
    .sellzio-live-badge {
      display: inline-flex;
      align-items: center;
      background-color: #ee4d2d;
      color: white;
      font-size: 8px;
      font-weight: 600;
      padding: 0 4px;
      height: 14px;
      border-radius: 3px;
      margin-right: 4px;
      margin-bottom: 4px;
      vertical-align: middle;
    }

    .sellzio-equalizer {
      margin-right: 4px;
      display: inline-flex;
      align-items: end;
      gap: 1px;
      height: 5px;
    }

    .sellzio-bar {
      width: 1px;
      background-color: white;
    }

    .sellzio-bar-1 {
      height: 1px;
      animation: sellzio-equalizer-1 1.5s infinite;
    }

    .sellzio-bar-2 {
      height: 3px;
      animation: sellzio-equalizer-2 1.5s infinite;
    }

    .sellzio-bar-3 {
      height: 2px;
      animation: sellzio-equalizer-3 1.5s infinite;
    }

    .sellzio-bar-4 {
      height: 1px;
      animation: sellzio-equalizer-1 1.5s infinite;
    }

    /* Live Corner Badge - di pojok kiri atas */
    .sellzio-live-corner-badge {
      position: absolute;
      top: 0;
      left: 0;
      background-color: #ee4d2d;
      color: white;
      font-size: 10px;
      font-weight: bold;
      padding: 2px 4px;
      border-bottom-right-radius: 3px;
      z-index: 10;
      display: flex;
      align-items: center;
    }

    .sellzio-live-dot {
      width: 8px;
      height: 8px;
      background-color: white;
      border-radius: 50%;
      margin-right: 2px;
      animation: sellzio-pulse 2s infinite;
    }

    /* Termurah di Toko Badge - sesuai Velozio */
    .sellzio-termurah-badge {
      position: relative;
      display: inline-flex;
      align-items: center;
      background-color: #ee4d2d;
      color: white;
      font-size: 10px;
      font-weight: 600;
      padding: 2px 8px 2px 20px;
      border-radius: 3px;
      margin-bottom: 6px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      margin-left: 12px;
    }

    .sellzio-termurah-icon {
      position: absolute;
      left: -10px;
      top: 50%;
      transform: translateY(-50%);
      background-color: white;
      color: #ee4d2d;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid #ee4d2d;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .sellzio-thumbs-up-icon {
      width: 16px;
      height: 16px;
    }

    /* Komisi Xtra Badge - sesuai Velozio */
    .sellzio-komisi-badge {
      display: inline-flex;
      align-items: center;
      background-color: white;
      border: 1px solid #ee4d2d;
      border-radius: 2px;
      padding: 2px 6px;
      font-size: 10px;
      font-weight: 600;
      margin-bottom: 6px;
    }

    .sellzio-komisi-text {
      color: #ee4d2d;
    }

    .sellzio-xtra-text {
      color: #005bac;
      margin-left: 2px;
    }

    /* Animations */
    @keyframes sellzio-pulse {
      0%, 100% {
        opacity: 1;
      }
      50% {
        opacity: 0.5;
      }
    }

    @keyframes sellzio-equalizer-1 {
      0%, 100% { height: 1px; }
      50% { height: 3px; }
    }

    @keyframes sellzio-equalizer-2 {
      0%, 100% { height: 3px; }
      50% { height: 1px; }
    }

    @keyframes sellzio-equalizer-3 {
      0%, 100% { height: 2px; }
      50% { height: 4px; }
    }
  `}</style>
)
