"use client"

import type React from "react"
import { useEffect, useRef, useState, useCallback } from "react"

interface SellzioMasonryLayoutProps {
  children: React.ReactNode[]
  columnCount?: { mobile: number; tablet: number; desktop: number }
  gap?: number
}

export const SellzioMasonryLayout = ({
  children,
  columnCount = { mobile: 2, tablet: 3, desktop: 4 },
  gap = 12,
}: SellzioMasonryLayoutProps) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const [columns, setColumns] = useState<React.ReactNode[][]>([])
  const [visibleCount, setVisibleCount] = useState(12)
  const observerRef = useRef<IntersectionObserver | null>(null)
  const loadingRef = useRef<HTMLDivElement>(null)
  const currentColumnCountRef = useRef(0)
  const [initialized, setInitialized] = useState(false)
  const [columnWidth, setColumnWidth] = useState("100%")

  // Get column count based on screen size
  const getColumnCount = useCallback(() => {
    if (typeof window === "undefined") return columnCount.desktop

    const width = window.innerWidth
    if (width < 768) return columnCount.mobile
    if (width < 1024) return columnCount.tablet
    return columnCount.desktop
  }, [columnCount])

  // Calculate column width
  const calculateColumnWidth = useCallback(() => {
    const count = getColumnCount()
    const totalGap = (count - 1) * gap
    const width = `calc((100% - ${totalGap}px) / ${count})`
    setColumnWidth(width)
  }, [getColumnCount, gap])

  // Distribute items into columns
  const distributeItems = useCallback(() => {
    const count = getColumnCount()
    currentColumnCountRef.current = count

    const visibleItems = children.slice(0, visibleCount)
    const newColumns: React.ReactNode[][] = Array.from({ length: count }, () => [])

    // Simple distribution - round robin
    visibleItems.forEach((child, index) => {
      const columnIndex = index % count
      newColumns[columnIndex].push(
        <div key={index} className="sellzio-masonry-item">
          {child}
        </div>,
      )
    })

    setColumns(newColumns)
    calculateColumnWidth()
  }, [children, getColumnCount, visibleCount, calculateColumnWidth])

  // Handle resize with throttling
  const handleResize = useCallback(() => {
    const newCount = getColumnCount()
    if (newCount !== currentColumnCountRef.current) {
      currentColumnCountRef.current = newCount
      distributeItems()
    }
    calculateColumnWidth()
  }, [getColumnCount, calculateColumnWidth, distributeItems])

  // Throttled resize handler
  const throttledResize = useCallback(() => {
    let timeoutId: NodeJS.Timeout
    return () => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(handleResize, 150)
    }
  }, [handleResize])

  // Setup intersection observer for lazy loading
  useEffect(() => {
    if (!loadingRef.current) return

    observerRef.current = new IntersectionObserver(
      (entries) => {
        const [entry] = entries
        if (entry.isIntersecting && visibleCount < children.length) {
          // Load next batch of items (6 at a time)
          setVisibleCount((prev) => Math.min(prev + 6, children.length))
        }
      },
      { threshold: 0.1 },
    )

    observerRef.current.observe(loadingRef.current)

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
    }
  }, [children.length, visibleCount])

  // Initial setup and when visible count changes
  useEffect(() => {
    if (!initialized) {
      distributeItems()
      setInitialized(true)
    } else if (visibleCount > 12) {
      // Only redistribute when loading more items
      distributeItems()
    }
  }, [distributeItems, initialized, visibleCount])

  // Setup resize listener
  useEffect(() => {
    const resizeHandler = throttledResize()
    window.addEventListener("resize", resizeHandler)

    // Initial calculation
    calculateColumnWidth()

    return () => {
      window.removeEventListener("resize", resizeHandler)
    }
  }, [throttledResize, calculateColumnWidth])

  return (
    <div ref={containerRef} className="sellzio-masonry-container">
      <div className="sellzio-masonry-columns" style={{ gap: `${gap}px` }}>
        {columns.map((column, columnIndex) => (
          <div
            key={columnIndex}
            className="sellzio-masonry-column"
            style={{
              width: columnWidth,
              gap: `${gap}px`,
            }}
          >
            {column}
          </div>
        ))}
      </div>

      {/* Loading trigger element */}
      {visibleCount < children.length && (
        <div ref={loadingRef} className="sellzio-masonry-loading">
          <div className="sellzio-loading-spinner"></div>
        </div>
      )}

      <style jsx>{`
        .sellzio-masonry-container {
          width: 100%;
        }

        .sellzio-masonry-columns {
          display: flex;
        }

        .sellzio-masonry-column {
          display: flex;
          flex-direction: column;
        }

        .sellzio-masonry-item {
          margin-bottom: ${gap}px;
        }

        .sellzio-masonry-loading {
          width: 100%;
          height: 40px;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-top: 16px;
        }

        .sellzio-loading-spinner {
          width: 32px;
          height: 32px;
          border: 4px solid #f3f4f6;
          border-top: 4px solid #f97316;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  )
}
