"use client"

import { useState } from "react"
import Image from "next/image"
import { SellzioLiveCornerBadge, SellzioRatingStars } from "./sellzio-badges"

interface SellzioVideoCardProps {
  thumbnail?: string
  videoSrc?: string
  isLive?: boolean
  productImage?: string
  productName?: string
  rating?: number
  sold?: number
  price?: string
  originalPrice?: string
  thumbnailSrc?: string
}

export const SellzioVideoCard = ({
  thumbnail,
  thumbnailSrc,
  videoSrc,
  isLive,
  productImage,
  productName,
  rating = 0,
  sold = 0,
  price,
  originalPrice,
}: SellzioVideoCardProps) => {
  const [playing, setPlaying] = useState(false)
  const [isLoaded, setIsLoaded] = useState(false)

  const handlePlay = () => {
    if (videoSrc) {
      setPlaying(true)
    }
  }

  const thumbnailImage = thumbnailSrc || thumbnail

  return (
    <div className="bg-white rounded-sm overflow-hidden shadow-sm hover:shadow-md transition-all duration-200 hover:-translate-y-0.5 cursor-pointer">
      <div className="relative aspect-[9/16] overflow-hidden bg-black">
        {!playing ? (
          <>
            <Image
              src={thumbnailImage || "/placeholder.svg"}
              alt="Video thumbnail"
              fill
              className={`object-cover transition-opacity duration-300 ${isLoaded ? "opacity-100" : "opacity-0"}`}
              onLoad={() => setIsLoaded(true)}
              sizes="(max-width: 768px) 50vw, 33vw"
            />
            {!isLoaded && <div className="absolute inset-0 bg-gray-200 animate-pulse"></div>}
            <button
              className="absolute inset-0 flex items-center justify-center z-10"
              onClick={handlePlay}
              aria-label="Play video"
            >
              <div className="w-12 h-12 rounded-full bg-black bg-opacity-50 flex items-center justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="white"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <polygon points="5 3 19 12 5 21 5 3" />
                </svg>
              </div>
            </button>
            {isLive && <SellzioLiveCornerBadge />}
          </>
        ) : (
          <iframe
            src={videoSrc}
            title="Video player"
            className="absolute inset-0 w-full h-full"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
          ></iframe>
        )}
      </div>

      <div className="p-2 flex items-center">
        <div className="w-10 h-10 relative flex-shrink-0">
          <Image
            src={productImage || "/placeholder.svg"}
            alt={productName || "Product"}
            fill
            className="object-contain"
            sizes="40px"
          />
        </div>
        <div className="ml-2 flex-grow min-w-0">
          <h3 className="text-xs text-gray-800 truncate">{productName || "Product Name"}</h3>
          <div className="flex items-center text-[10px] text-gray-500">
            <SellzioRatingStars rating={rating} />
            <div className="mx-1 w-[1px] h-2 bg-gray-300"></div>
            <span>Terjual {sold}</span>
          </div>
          <div className="flex items-center">
            <div className="text-xs font-bold text-[#ee4d2d]">{price || "Rp 0"}</div>
            {originalPrice && (
              <div className="ml-1 text-[10px] text-orange-500 flex items-center overflow-hidden text-ellipsis max-w-[40%]">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-2 h-2">
                  <path fillRule="evenodd" d="M1.5 6.375c0-1.036.84-1.875 1.875-1.875h17.25c1.035 0 1.875.84 1.875 1.875v3.026a.75.75 0 01-.375.65 2.249 2.249 0 000 3.898.75.75 0 01.375.65v3.026c0 1.035-.84 1.875-1.875 1.875H3.375A1.875 1.875 0 011.5 17.625v-3.026a.75.75 0 01.374-.65 2.249 2.249 0 000-3.898.75.75 0 01-.374-.65V6.375zm15-1.125a.75.75 0 01.75.75v.75a.75.75 0 01-1.5 0V6a.75.75 0 01.75-.75zm.75 4.5a.75.75 0 00-1.5 0v.75a.75.75 0 001.5 0v-.75zm-.75 3a.75.75 0 01.75.75v.75a.75.75 0 01-1.5 0V15a.75.75 0 01.75-.75zm.75 4.5a.75.75 0 00-1.5 0v.75a.75.75 0 001.5 0V18zM6 12a.75.75 0 01.75-.75H12a.75.75 0 010 1.5H6.75A.75.75 0 016 12zm.75 2.25a.75.75 0 000 1.5h3a.75.75 0 000-1.5h-3z" clipRule="evenodd" />
                  <path d="M9 12l2 2 4-4" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" fill="none"/>
                </svg>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
