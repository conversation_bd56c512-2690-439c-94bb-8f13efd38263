"use client"

import React, { useState } from "react"
import Image from "next/image"
import {
  SellzioMallBadge,
  SellzioStarBadge,
  SellzioStarLiteBadge,
  SellzioCodBadge,
  SellzioDiscountBadge,
  SellzioRatingStars,
  SellzioShippingBadge,
  SellzioTerlarisBadge,
  SellzioLiveBadge,
  SellzioLiveCornerBadge,
  SellzioTermurahDiTokoBadge,
  SellzioKomisiXtraBadge,
  SellzioBadgeStyles,
} from "./sellzio-badges"
import { SellzioVideoCard } from "./sellzio-video-card"
import { SellzioImageSliderCard } from "./sellzio-image-slider-card"
import { SellzioFlashSaleCard } from "./sellzio-flash-sale-card"

export type SellzioBadgeType = "mall" | "star" | "star-lite" | "termurah" | "terlaris" | "komisi-xtra" | "none"
export type SellzioCardType = "standard" | "video" | "image-slider" | "flash-sale" | "promo" | "image-only" | "flash-sale" | "image-slider"

export interface SellzioProductCardProps {
  id: number
  type?: SellzioCardType
  name: string
  price: string
  originalPrice?: string
  discount?: string
  image: string
  images?: string[] // For image slider
  rating?: number
  sold?: number
  hasCod?: boolean
  isMall?: boolean
  isTerlaris?: boolean
  isLive?: boolean
  shipping?: string
  badgeType?: SellzioBadgeType
  videoThumbnail?: string
  videoSrc?: string
  // Flash sale specific props
  endTime?: Date
  stockSold?: number
  totalStock?: number
  onClick?: () => void
}

export const SellzioProductCard: React.FC<SellzioProductCardProps> = ({
  id,
  type = "standard",
  name,
  price,
  originalPrice,
  discount,
  image,
  images = [],
  rating = 0,
  sold = 0,
  hasCod = false,
  isMall = false,
  isTerlaris = false,
  isLive = false,
  shipping = "reguler",
  badgeType = "none",
  videoThumbnail,
  videoSrc,
  endTime,
  stockSold = 0,
  totalStock = 100,
  onClick,
}) => {
  const [isLoaded, setIsLoaded] = useState(false)
  const [imageError, setImageError] = useState(false)

  const handleImageError = () => {
    setImageError(true)
  }

  const getShippingType = (): "instan" | "gratis" | "reguler" => {
    if (shipping?.toLowerCase().includes("instan")) return "instan"
    if (shipping?.toLowerCase().includes("gratis")) return "gratis"
    return "reguler"
  }

  // Render different card types
  if (type === "video" && videoThumbnail) {
    return (
      <SellzioVideoCard
        thumbnailSrc={videoThumbnail}
        videoSrc={videoSrc}
        isLive={isLive}
        productImage={image}
        productName={name}
        rating={rating}
        sold={sold}
        price={price}
        originalPrice={originalPrice}
      />
    )
  }

  if (type === "image-slider" && images.length > 1) {
    return (
      <SellzioImageSliderCard
        id={id}
        name={name}
        price={price}
        originalPrice={originalPrice}
        discount={discount}
        images={images}
        rating={rating}
        sold={sold}
        hasCod={hasCod}
        isMall={isMall}
        badgeType={badgeType}
        shipping={shipping}
        onClick={onClick}
      />
    )
  }

  if (type === "flash-sale" && originalPrice && discount) {
    return (
      <SellzioFlashSaleCard
        id={id}
        name={name}
        price={price}
        originalPrice={originalPrice}
        discount={discount}
        image={image}
        rating={rating}
        sold={sold}
        hasCod={hasCod}
        isMall={isMall}
        badgeType={badgeType}
        shipping={shipping}
        endTime={endTime}
        stockSold={stockSold}
        totalStock={totalStock}
        onClick={onClick}
      />
    )
  }

  return (
    <>
      <SellzioBadgeStyles />
      <div
        className="sellzio-product-card"
        onClick={onClick}
      >
        {/* Image Container */}
        <div className="sellzio-product-image-container">
          <Image
            src={imageError ? "/placeholder.svg" : image}
            alt={name}
            fill
            className={`sellzio-product-image ${isLoaded ? "loaded" : ""}`}
            onLoad={() => setIsLoaded(true)}
            onError={handleImageError}
            sizes="(max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
          />
          
          {/* Loading placeholder */}
          {!isLoaded && (
            <div className="sellzio-image-placeholder">
              <div className="sellzio-loading-shimmer"></div>
            </div>
          )}

          {/* Badges on image */}
          {discount && <SellzioDiscountBadge discount={discount} />}
          {hasCod && <SellzioCodBadge />}
          {isLive && <SellzioLiveCornerBadge />}
        </div>

        {/* Product Info */}
        <div className="sellzio-product-info">
          {/* Product Name with badges */}
          <h3 className="sellzio-product-name">
            {/* Render badges based on priority */}
            {isLive ? (
              <SellzioLiveBadge />
            ) : badgeType === "mall" || isMall ? (
              <SellzioMallBadge />
            ) : badgeType === "star" ? (
              <SellzioStarBadge />
            ) : badgeType === "star-lite" ? (
              <SellzioStarLiteBadge />
            ) : null}
            {name}
          </h3>

          {/* Special badges di bawah nama produk */}
          {badgeType === "termurah" && <SellzioTermurahDiTokoBadge />}
          {(badgeType === "terlaris" || isTerlaris) && <SellzioTerlarisBadge />}
          {badgeType === "komisi-xtra" && <SellzioKomisiXtraBadge />}

          {/* Rating di bawah special badges */}
          <SellzioRatingStars rating={rating} />

          {/* Price dengan terjual di kanan */}
          <div className="sellzio-price-container" data-long-price={price.length > 10}>
            <div className="sellzio-price-left">
              <div className="sellzio-current-price">{price}</div>
              {originalPrice && discount && (
                <div className="sellzio-discount-ticket">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-4 h-4">
                    <path fillRule="evenodd" d="M1.5 6.375c0-1.036.84-1.875 1.875-1.875h17.25c1.035 0 1.875.84 1.875 1.875v3.026a.75.75 0 01-.375.65 2.249 2.249 0 000 3.898.75.75 0 01.375.65v3.026c0 1.035-.84 1.875-1.875 1.875H3.375A1.875 1.875 0 011.5 17.625v-3.026a.75.75 0 01.374-.65 2.249 2.249 0 000-3.898.75.75 0 01-.374-.65V6.375zm15-1.125a.75.75 0 01.75.75v.75a.75.75 0 01-1.5 0V6a.75.75 0 01.75-.75zm.75 4.5a.75.75 0 00-1.5 0v.75a.75.75 0 001.5 0v-.75zm-.75 3a.75.75 0 01.75.75v.75a.75.75 0 01-1.5 0V15a.75.75 0 01.75-.75zm.75 4.5a.75.75 0 00-1.5 0v.75a.75.75 0 001.5 0V18zM6 12a.75.75 0 01.75-.75H12a.75.75 0 010 1.5H6.75A.75.75 0 016 12zm.75 2.25a.75.75 0 000 1.5h3a.75.75 0 000-1.5h-3z" clipRule="evenodd" />
                    <path d="M9 12l2 2 4-4" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" fill="none"/>
                  </svg>
                </div>
              )}
            </div>
            <div className="sellzio-sold-count">Terjual {sold}</div>
          </div>

          {/* Shipping di bawah harga */}
          <SellzioShippingBadge type={getShippingType()} city="Jakarta" />
        </div>

        <style jsx>{`
          .sellzio-product-card {
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all 0.2s ease;
            width: 100%;
            border: 1px solid #f3f4f6;
          }

          .sellzio-product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
          }

          .sellzio-product-image-container {
            position: relative;
            width: 100%;
            padding-top: 100%; /* 1:1 Aspect Ratio */
            overflow: hidden;
            background-color: #f9fafb;
          }

          .sellzio-product-image {
            object-fit: contain;
            transition: opacity 0.3s ease;
            opacity: 0;
            padding: 8px;
          }

          .sellzio-product-image.loaded {
            opacity: 1;
          }

          .sellzio-image-placeholder {
            position: absolute;
            inset: 0;
            background-color: #f3f4f6;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .sellzio-loading-shimmer {
            width: 60%;
            height: 60%;
            background: linear-gradient(
              90deg,
              #f3f4f6 25%,
              #e5e7eb 50%,
              #f3f4f6 75%
            );
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
            border-radius: 4px;
          }

          @keyframes shimmer {
            0% {
              background-position: -200% 0;
            }
            100% {
              background-position: 200% 0;
            }
          }

          .sellzio-product-info {
            padding: 12px;
          }

          .sellzio-product-name {
            font-size: 13px;
            font-weight: 500;
            color: #374151;
            line-height: 1.4;
            margin-bottom: 8px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          /* Force rating to be visible */
          .sellzio-product-info > div:nth-child(3) {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
          }







          .sellzio-price-container {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-top: 8px;
          }

          .sellzio-price-left {
            display: flex;
            align-items: center;
            gap: 8px;
          }

          .sellzio-current-price {
            font-size: 14px;
            font-weight: 700;
            color: #f97316;
          }

          .sellzio-discount-ticket {
            color: #f97316;
            display: flex;
            align-items: center;
          }

          .sellzio-sold-count {
            font-size: 11px;
            color: #6b7280;
            white-space: nowrap;
          }

          @media (max-width: 768px) {
            .sellzio-product-card {
              border-radius: 6px;
            }

            .sellzio-product-info {
              padding: 10px;
            }

            .sellzio-product-name {
              font-size: 12px;
            }

            .sellzio-current-price {
              font-size: 13px;
            }

            .sellzio-sold-count {
              font-size: 9px;
              flex-shrink: 0;
              white-space: nowrap;
            }

            .sellzio-price-container {
              display: flex;
              align-items: center;
              justify-content: space-between;
              gap: 8px;
              flex-wrap: nowrap;
            }

            .sellzio-price-left {
              display: flex;
              align-items: center;
              gap: 4px;
              flex: 1;
              min-width: 0;
              overflow: hidden;
            }

            .sellzio-current-price {
              flex-shrink: 1;
              min-width: 60px;
              max-width: 120px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .sellzio-discount-ticket {
              flex-shrink: 0;
            }

            /* Desktop/Tablet: Semua elemen wajib ada dan sejajar */
            @media (min-width: 769px) {
              .sellzio-price-container {
                display: flex;
                align-items: center;
                justify-content: space-between;
                gap: 8px;
              }

              .sellzio-price-left {
                display: flex;
                align-items: center;
                gap: 8px;
              }

              .sellzio-discount-ticket {
                display: flex !important;
              }

              .sellzio-sold-count {
                display: block !important;
              }
            }

            /* Mobile priority: harga panjang = tiket, harga pendek = terjual */
            @media (max-width: 768px) {
              .sellzio-current-price {
                max-width: 110px;
              }

              /* Jika harga panjang (>10 karakter), sembunyikan terjual, tampilkan tiket */
              .sellzio-price-container[data-long-price="true"] .sellzio-sold-count {
                display: none;
              }

              .sellzio-price-container[data-long-price="true"] .sellzio-discount-ticket {
                display: flex;
              }

              /* Jika harga pendek, sembunyikan tiket, tampilkan terjual */
              .sellzio-price-container[data-long-price="false"] .sellzio-discount-ticket {
                display: none;
              }

              /* Harga pendek wajib sejajar dengan terjual */
              .sellzio-price-container[data-long-price="false"] {
                display: flex;
                align-items: center;
                justify-content: space-between;
                gap: 8px;
                flex-wrap: nowrap;
              }
            }
          }
        `}</style>
      </div>
    </>
  )
}
